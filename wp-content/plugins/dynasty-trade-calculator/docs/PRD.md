# Dynasty Trade Calculator - PRD

## Status (June 21, 2025)

### ✅ Completed
- **Fatal Error Fix**: Resolved PHP dependency issues, calculator loads without errors
- **Class-Based Migration**: Migrated membership functionality from functions to classes
- **Test Suite**: 50 tests, 370 assertions - ALL PASSING ✅
- **Debug System**: Conditional logging with `DTC_DEBUG` and `DTC_DEBUG_VERBOSE` constants
- **Shortcodes**: Recovered missing `dtc_register_form` and `dtc-easy-pricing-table`
- **RotoGPT Integration**: Complete subscription management with upgrade/downgrade logic

### 🔧 Debug Configuration (Essential)
Add to `wp-config.php`:
```php
// Basic debug (production: false)
define('DTC_DEBUG', true);
// Verbose debug with object dumps (production: false)
define('DTC_DEBUG_VERBOSE', true);
```

## 🔄 Current Focus (June 2025)
**RotoGPT Subscription Management** - Debugging downgrade scheduling issues and ensuring proper tier transitions

### 🔄 In Progress
- [ ] **Debug Downgrade Timing**: Fix issue where downgrades activate immediately instead of at expiration
- [ ] **PRD Documentation**: Update with comprehensive RotoGPT integration details

### ⏳ Pending
- [ ] **Update Remaining Files**: Find and replace old membership functions with class methods
- [ ] **Calculator Class Migration**: Convert calculator functionality to class-based approach
- [ ] **API Class Migration**: Convert REST API functionality to class-based approach
- [ ] **Admin Class Migration**: Convert admin functionality to class-based approach
- [ ] **Remove Old Files**: Delete function-based files after migration complete
- [ ] **Expand README.md**: Add proper project documentation for GitHub
- [ ] **Performance Testing**: Verify no performance regression after migration
- [ ] **Production Deployment**: Deploy class-based version to production

## 🏗️ Architecture
```
src/
├── Plugin.php      # Main initialization
├── Membership.php  # Complete membership functionality + RotoGPT integration
├── Debug.php       # Conditional logging
└── RestApi.php     # REST API endpoints
```

## 🔗 RotoGPT Integration

### Membership Level Mapping
Our system maps RCP membership levels to RotoGPT subscription types using `DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE`:

| RCP Level ID | Level Name | Price | RotoGPT Type |
|--------------|------------|-------|--------------|
| 8 | DTC Partner - Free | $0 | `free` |
| 7 | Calculator with ChatDTC (50 Credits) | $4.99 | `standard_50` |
| 9 | Calculator with ChatDTC (100 Credits) | $6.99 | `standard_100` |
| 12 | Calculator with ChatDTC (Unlimited) | $9.99 | `standard_200` |
| 5 | DTC Admin Member | $0 | `admin` |

### Upgrade/Downgrade Logic
**Key Innovation**: We determine upgrades vs downgrades by **subscription type hierarchy** (not price like default RCP):
```php
// Hierarchy: free → standard_50 → standard_100 → standard_200 → admin
$rotogpt_subscriptions = array_column(DTC_MEMBERSHIP_LEVEL_TO_ROTOGPT_SUBSCRIPTION_TYPE, 'rotogpt_subscription_type');
$is_upgrade = ($new_index > $old_index);
```

### Custom Downgrade Scheduling
**Critical Feature**: Unlike RCP's immediate downgrades, we preserve the higher tier until expiration:

1. **Upgrade**: Applied immediately to RotoGPT
2. **Downgrade**:
   - Higher tier stays active until expiration date
   - Lower tier scheduled as 'pending' status
   - Activates automatically when higher tier expires
   - RotoGPT receives scheduled downgrade with `new_subscription_start_date`

### API Endpoints
- **Create**: `/subscriptions/create` - New user signups
- **Update**: `/subscriptions/update` - Upgrades/downgrades with scheduling
- **Signin**: `/signin` - Authentication for API calls

## ✅ Recent Fixes (June 2025)

### Downgrade Scheduling Bug - RESOLVED
**Problem**: Custom downgrade scheduling wasn't working properly - RotoGPT was being updated immediately even for scheduled downgrades.

**Root Cause**: Critical logic flaw in `handleNewMembershipAdded()` method:
- After scheduling a downgrade (setting old membership active, new membership pending), the code continued executing
- This caused `rotoGptUpdateSubscription()` to be called immediately, defeating the scheduling purpose

**Solution Implemented**:
1. **Early Return**: Added `return;` statement after successful downgrade scheduling (line 607)
2. **RotoGPT Integration**: Enhanced `handleMembershipStatusTransition()` to update RotoGPT when pending downgrades activate
3. **Comprehensive Testing**: Added `DowngradeSchedulingTest.php` with 7 test cases covering all scenarios

**Files Modified**:
- `src/Membership.php` - Lines 605-607 (early return), Lines 505-519 (RotoGPT activation)
- `tests/DowngradeSchedulingTest.php` - New comprehensive test suite

**Verification**: All 80 tests passing (679 assertions) ✅

## 🧪 Testing
- **9 Test Files**: 80 tests, 679 assertions - ALL PASSING ✅
- **Mock Data Only**: No secrets or wp-config data in tests (git-safe)
- **Run Tests**: `vendor/bin/phpunit` (~0.08 seconds)
- **Comprehensive Coverage**: Basic, Debug, Membership, API, Edge Cases, Lifecycle, Downgrade Scheduling tests
- **Acceptance Tests**: Complete subscription lifecycle and RotoGPT integration scenarios

## 🔄 Migration Pattern
```php
// OLD: dtc_get_current_user_customer()
// NEW: Membership::getCurrentUserCustomer()
// Add: use DynastyTradeCalculator\Membership;
```
